from odf.opendocument import load
from odf.table import Table
from odf.text import P
from copy import deepcopy

# Load the document
doc = load("Template.odt")

# Get the body (a container of elements)
body = doc.text

# Get all child elements of the body as a list
elements = body.childNodes

# Find the original table named "Table1"
original_table = None
table_index = -1
for i, elem in enumerate(elements):
    if isinstance(elem, Table):
        table_name = elem.getAttribute("name")
        if table_name == "Table1":
            original_table = elem
            table_index = i
            break

if original_table is None:
    raise ValueError('Table named "Table1" not found in the document.')

# Prepare a new list of elements
new_elements = []

# Add all elements up to and including the original table
for i in range(table_index + 1):
    new_elements.append(elements[i])

# Insert 5 duplicates after the original table
for i in range(5):
    # Add a blank paragraph
    new_elements.append(P())

    # Duplicate the table
    new_table = deepcopy(original_table)
    # Optionally rename to avoid conflicts
    new_table.setAttribute("name", f"Table1_Duplicate_{i+1}")

    # Append the duplicated table
    new_elements.append(new_table)

# Add any remaining elements after the original table (if any)
for i in range(table_index + 1, len(elements)):
    new_elements.append(elements[i])

# Clear the current body content
while body.firstChild:
    body.removeChild(body.firstChild)

# Append all new elements to the body
for elem in new_elements:
    body.appendChild(elem)

# Save the modified document
doc.save("Output.odt")
print("Tables duplicated successfully!")